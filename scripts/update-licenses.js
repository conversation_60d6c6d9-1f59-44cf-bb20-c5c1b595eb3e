import fs from 'node:fs';
import path from 'node:path';

import * as checker from 'license-checker';

const ROOT_PATH = path.join(import.meta.dirname, '..');

// Helper function to format package name with optional link
function formatPackageName(name, repository, useHtml = false) {
  // Extract just the package name part (without @ and version)
  const { name: packageName } = parsePackageNameVersion(name);

  if (repository) {
    if (useHtml) {
      // For scoped packages like @babel/core@7.24.5, format as @<b>babel/core</b>@7.24.5
      if (packageName.startsWith('@')) {
        const atIndex = name.indexOf('@', 1); // Find the @ before version
        if (atIndex !== -1) {
          const nameWithoutVersion = name.substring(0, atIndex);
          const version = name.substring(atIndex);
          return `<a href="${repository}">@<b>${nameWithoutVersion.substring(
            1,
          )}</b>${version}</a>`;
        } else {
          return `<a href="${repository}">@<b>${packageName.substring(
            1,
          )}</b></a>`;
        }
      } else {
        // For regular packages like ansi-regex@6.0.1, format as <b>ansi-regex</b>@6.0.1
        const atIndex = name.indexOf('@');
        if (atIndex !== -1) {
          const nameWithoutVersion = name.substring(0, atIndex);
          const version = name.substring(atIndex);
          return `<a href="${repository}"><b>${nameWithoutVersion}</b>${version}</a>`;
        } else {
          return `<a href="${repository}"><b>${packageName}</b></a>`;
        }
      }
    }
    return `[${name}](${repository})`;
  }
  return `**${packageName}**`;
}

// Helper function to get package scope/group
function getPackageGroup(name) {
  if (name.startsWith('@')) {
    const scopeEnd = name.indexOf('/');
    if (scopeEnd !== -1) {
      return name.substring(0, scopeEnd);
    }
  }
  return 'Other Packages';
}

// Helper function to extract package name and version
function parsePackageNameVersion(fullName) {
  // Handle scoped packages like @babel/core@7.24.5
  if (fullName.startsWith('@')) {
    const match = fullName.match(/^(@[^/]+\/[^@]+)@(.+)$/);
    if (match) {
      return { name: match[1], version: match[2] };
    }
  } else {
    // Handle regular packages like ansi-regex@6.0.1
    const match = fullName.match(/^([^@]+)@(.+)$/);
    if (match) {
      return { name: match[1], version: match[2] };
    }
  }
  // Fallback if no version found
  return { name: fullName, version: null };
}

// Helper function to render a single package
function renderPackage(name, summary, indent = '') {
  const license = processLicenseText(summary);

  if (license === 'No license text available') {
    return `${indent}- ${formatPackageName(name, summary.repository)} - ${
      summary.licenses
    }\n`;
  }

  // Indent each line of the license text to maintain list structure
  const indentedLicense = license
    .split('\n')
    .map((line) => (line.trim() ? `${indent}  ${line}` : ''))
    .join('\n');

  return `${indent}- <details><summary>${formatPackageName(
    name,
    summary.repository,
    true,
  )} - ${
    summary.licenses
  }</summary>\n\n${indent}  \`\`\`\n${indentedLicense}\n${indent}  \`\`\`\n\n${indent}  </details>\n\n`;
}

// Helper function to get unique licenses from a group of packages
function getGroupLicenses(packages) {
  const licenses = new Set();
  for (const [, summary] of packages) {
    // Handle multiple licenses separated by various delimiters
    const packageLicenses = summary.licenses
      .split(/[,;]|\s+OR\s+|\s+AND\s+/i)
      .map((l) => l.trim())
      .filter((l) => l.length > 0);
    packageLicenses.forEach((license) => licenses.add(license));
  }
  return Array.from(licenses).sort().join(', ');
}

// Helper function to process license text consistently
function processLicenseText(summary) {
  let license =
    summary.licenseText ??
    (summary.licenseFile ? fs.readFileSync(summary.licenseFile, 'utf8') : null);

  if (!license) {
    return 'No license text available';
  }

  if (
    summary.licenseFile &&
    summary.licenseFile.toLowerCase().endsWith('readme.md') &&
    license.toLowerCase().indexOf('license') === -1
  ) {
    return 'No license text available';
  }

  // Remove HTML tags, trim whitespace, and escape HTML entities
  license = license
    .replace(/<[^>]+>/g, '')
    .trim()
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');

  return license;
}

// Helper function to group packages by identical license text
function groupByLicenseText(packages) {
  const licenseGroups = {};

  for (const [name, summary] of packages) {
    const licenseText = processLicenseText(summary);
    if (!licenseGroups[licenseText]) {
      licenseGroups[licenseText] = [];
    }
    licenseGroups[licenseText].push([name, summary]);
  }

  return licenseGroups;
}

// Helper function to render packages grouped by license text
function renderLicenseGroups(packages, indent = '') {
  const licenseGroups = groupByLicenseText(packages);
  const licenseTexts = Object.keys(licenseGroups);

  // Separate packages with and without license text
  const noLicensePackages = licenseGroups['No license text available'] || [];
  const licensedGroups = Object.keys(licenseGroups).filter(
    (key) => key !== 'No license text available',
  );

  let result = '';

  // Handle packages without license text first (render as simple list items)
  for (const [name, summary] of noLicensePackages) {
    result += `${indent}- ${formatPackageName(name, summary.repository)} - ${
      summary.licenses
    }\n`;
  }

  if (licensedGroups.length === 1) {
    // All remaining packages have the same license text - render as simple list
    const licenseText = licensedGroups[0];
    const packagesInGroup = licenseGroups[licenseText];

    for (const [name, summary] of packagesInGroup) {
      result += `${indent}- ${formatPackageName(
        name,
        summary.repository,
        true,
      )} - ${summary.licenses}\n`;
    }

    // Add the license text
    const indentedLicense = licenseText
      .split('\n')
      .map((line) => (line.trim() ? `${indent}  ${line}` : ''))
      .join('\n');

    result += `\n${indent}\`\`\`\n${indentedLicense}\n${indent}\`\`\`\n\n`;
    return result;
  } else if (licensedGroups.length > 1) {
    // Multiple license texts - use details/summary for each group
    for (const licenseText of licensedGroups) {
      const packagesInGroup = licenseGroups[licenseText];

      // Create package list for summary
      const packageList = packagesInGroup
        .map(
          ([name, summary]) =>
            `${formatPackageName(name, summary.repository, true)} - ${
              summary.licenses
            }`,
        )
        .join('<br>');

      result += `${indent}- <details>\n${indent}  <summary>\n${indent}  ${packageList}\n${indent}  </summary>\n\n`;

      // Add the license text
      const indentedLicense = licenseText
        .split('\n')
        .map((line) => (line.trim() ? `${indent}  ${line}` : ''))
        .join('\n');

      result += `${indent}  \`\`\`\n${indentedLicense}\n${indent}  \`\`\`\n\n${indent}  </details>\n\n`;
    }
  }

  return result;
}

let md = `# Third-Party Notices

This project uses the following third-party software under their respective licenses. Click a package to expand the full license text.

---

`;

try {
  await new Promise((resolve, reject) =>
    checker.init(
      {
        start: ROOT_PATH,
        production: true,
        excludePrivatePackages: true,
      },
      (error, summaries) => {
        if (error) {
          reject(error);
          return;
        }

        try {
          // Group packages by scope
          const groups = {};

          for (const [name, summary] of Object.entries(summaries)) {
            const group = getPackageGroup(name);
            if (!groups[group]) {
              groups[group] = [];
            }
            groups[group].push([name, summary]);
          }

          // Sort groups and packages within groups
          const sortedGroups = Object.keys(groups).sort((a, b) =>
            a.localeCompare(b),
          );

          for (const groupName of sortedGroups) {
            const packages = groups[groupName].sort(([a], [b]) =>
              a.localeCompare(b),
            );

            if (packages.length === 1) {
              // Don't group if there's only one package
              for (const [name, summary] of packages) {
                md += renderPackage(name, summary);
              }
            } else if (groupName === 'Other Packages') {
              // Handle "Other Packages" with version grouping but no scope grouping
              const packageGroups = {};
              for (const [fullName, summary] of packages) {
                const { name } = parsePackageNameVersion(fullName);
                if (!packageGroups[name]) {
                  packageGroups[name] = [];
                }
                packageGroups[name].push([fullName, summary]);
              }

              const sortedPackageNames = Object.keys(packageGroups).sort();
              for (const packageName of sortedPackageNames) {
                const packageVersions = packageGroups[packageName].sort(
                  ([a], [b]) => a.localeCompare(b),
                );

                if (packageVersions.length === 1) {
                  // Single version, render directly
                  const [fullName, summary] = packageVersions[0];
                  md += renderPackage(fullName, summary);
                } else {
                  // Multiple versions, group them by license text
                  const versions = packageVersions
                    .map(([fullName]) => {
                      const { version } = parsePackageNameVersion(fullName);
                      return version;
                    })
                    .join(', ');
                  const versionGroupLicenses =
                    getGroupLicenses(packageVersions);

                  md += `- <details><summary><b>${packageName}</b> @ ${versions} - ${versionGroupLicenses}</summary>\n\n`;
                  md += renderLicenseGroups(packageVersions, '  ');
                  md += `  </details>\n\n`;
                }
              }
            } else {
              // Group scoped packages under collapsible sections with count and licenses
              const groupLicenses = getGroupLicenses(packages);
              md += `- <details><summary><b>${groupName}</b> (${packages.length}) - ${groupLicenses}</summary>\n\n`;

              // Sub-group by package name (for version grouping)
              const packageGroups = {};
              for (const [fullName, summary] of packages) {
                const { name } = parsePackageNameVersion(fullName);
                if (!packageGroups[name]) {
                  packageGroups[name] = [];
                }
                packageGroups[name].push([fullName, summary]);
              }

              const sortedPackageNames = Object.keys(packageGroups).sort();

              // Collect all packages for license text grouping
              const allPackagesInGroup = [];
              for (const packageName of sortedPackageNames) {
                const packageVersions = packageGroups[packageName].sort(
                  ([a], [b]) => a.localeCompare(b),
                );
                allPackagesInGroup.push(...packageVersions);
              }

              // Group by license text and render
              md += renderLicenseGroups(allPackagesInGroup, '  ');
              md += `  </details>\n\n`;
            }
          }

          // Generate license summary
          const licenseCounts = {};
          for (const [name, summary] of Object.entries(summaries)) {
            // Handle multiple licenses separated by various delimiters
            const packageLicenses = summary.licenses
              .split(/[,;]|\s+OR\s+|\s+AND\s+/i)
              .map((l) => l.trim())
              .map((l) => l.replace(/^\*+|\*+$/g, '')) // Remove leading/trailing asterisks
              .map((l) => l.replace(/^\(+|\)+$/g, '')) // Remove leading/trailing parentheses
              .map((l) => l.trim())
              .filter((l) => l.length > 0);

            packageLicenses.forEach((license) => {
              if (!licenseCounts[license]) {
                licenseCounts[license] = 0;
              }
              licenseCounts[license]++;
            });
          }

          // Sort licenses by count (descending) then alphabetically
          const sortedLicenses = Object.entries(licenseCounts).sort(
            ([a, countA], [b, countB]) => {
              if (countB !== countA) return countB - countA;
              return a.localeCompare(b);
            },
          );

          // Add summary section
          md += `---\n\n## License Summary\n\n`;
          md += `This project uses **${
            Object.keys(summaries).length
          }** third-party packages under the following licenses:\n\n`;

          for (const [license, count] of sortedLicenses) {
            md += `- **${license}**: ${count} package${count > 1 ? 's' : ''}\n`;
          }

          md += `\n`;

          resolve();
        } catch (processingError) {
          reject(processingError);
        }
      },
    ),
  );

  fs.writeFileSync(path.join(ROOT_PATH, 'THIRD-PARTY-NOTICES.md'), md);
  console.log('✅ THIRD-PARTY-NOTICES.md updated successfully');
} catch (error) {
  console.error('❌ Script failed:', error.message);
  process.exit(1);
}
